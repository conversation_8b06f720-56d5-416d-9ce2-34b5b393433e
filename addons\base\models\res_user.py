"""
User model - System users and authentication

This model represents system users, inheriting from res.partner
and adding authentication and access control features.
Similar to Odoo's res.users model.
"""

import hashlib
import secrets
from datetime import datetime, timedelta

from erp.models.base import BaseModel
from erp import fields
from erp.api import depends


class ResUser(BaseModel):
    """System users with authentication and access control"""
    
    _name = 'res.users'
    _description = 'Users'
    _table = 'res_users'
    _inherits = {'res.partner': 'partner_id'}  # Inherit from partner
    
    # Partner relationship (inheritance)
    partner_id = fields.Many2One('res.partner', string='Related Partner', required=True,
                         ondelete='restrict', auto_join=True,
                         help='Partner-related data of the user')

    # Authentication fields
    login = fields.Char(string='Login', required=True, size=64, unique=True,
                help='Used to log into the system')

    password = fields.Char(string='Password', size=128,
                   help='Keep empty if you don\'t want the user to be able to connect')

    password_crypt = fields.Char(string='Encrypted Password', size=128,
                         help='Encrypted password for secure storage')

    # API access
    api_key = fields.Char(string='API Key', size=64,
                  help='API key for programmatic access')

    # Groups and permissions
    groups_id = fields.Many2Many('res.groups', 'res_groups_users_rel', 'uid', 'gid',
                         string='Groups', help='Groups that this user belongs to')

    # User status and control
    active = fields.Boolean(string='Active', default=True,
                    help='Uncheck to disable the user account')

    share = fields.Boolean(string='Share User', default=False, compute='_compute_share',
                   help='External user with limited access')

    # Login tracking
    login_date = fields.Datetime(string='Latest Connection', readonly=True,
                         help='Latest connection date')

    # User preferences
    action_id = fields.Many2One('ir.actions.actions', string='Home Action',
                        help='Action to open when user logs in')

    menu_id = fields.Many2One('ir.ui.menu', string='Menu Action',
                      help='Menu to show when user logs in')

    # Signature for emails/documents
    signature = fields.Text(string='Email Signature',
                    help='Email signature for this user')

    # Notification preferences
    notification_type = fields.Selection([
        ('email', 'Handle by Emails'),
        ('inbox', 'Handle in ERP'),
    ], string='Notification', default='email', required=True,
       help='Policy to receive notifications')
    
    # Session management
    session_token = fields.Char(string='Session Token', size=64,
                        help='Current session token')

    session_expiry = fields.Datetime(string='Session Expiry',
                             help='When current session expires')

    # Security
    login_attempts = fields.Integer(string='Failed Login Attempts', default=0,
                           help='Number of consecutive failed login attempts')

    locked_until = fields.Datetime(string='Locked Until',
                           help='Account locked until this time')

    # Two-factor authentication
    totp_secret = fields.Char(string='TOTP Secret', size=32,
                      help='Secret for two-factor authentication')

    totp_enabled = fields.Boolean(string='Two-Factor Authentication', default=False,
                          help='Enable two-factor authentication for this user')
    
    @depends('groups_id')
    def _compute_share(self):
        """Compute if user is a share user based on groups"""
        for user in self:
            # A user is a share user if they only have share groups
            share_groups = user.groups_id.filtered(lambda g: g.share)
            non_share_groups = user.groups_id.filtered(lambda g: not g.share)
            user.share = bool(share_groups) and not bool(non_share_groups)
    
    async def create(self, values):
        """Override create to handle password encryption and partner creation"""
        # Handle password encryption
        if 'password' in values and values['password']:
            values['password_crypt'] = self._encrypt_password(values['password'])
            # Don't store plain password
            del values['password']
        
        # Create partner if not provided
        if 'partner_id' not in values:
            partner_values = {}
            for field in ['name', 'email', 'phone', 'mobile', 'lang', 'tz']:
                if field in values:
                    partner_values[field] = values[field]
            
            partner = await self.env['res.partner'].create(partner_values)
            values['partner_id'] = partner.id
        
        # Generate API key
        if 'api_key' not in values:
            values['api_key'] = self._generate_api_key()
        
        return await super().create(values)
    
    async def write(self, values):
        """Override write to handle password changes"""
        if 'password' in values and values['password']:
            values['password_crypt'] = self._encrypt_password(values['password'])
            # Don't store plain password
            del values['password']
        
        return await super().write(values)
    
    def _encrypt_password(self, password):
        """Encrypt password using secure hashing"""
        if not password:
            return False
        
        # Use PBKDF2 with SHA256
        salt = secrets.token_hex(16)
        key = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), 
                                 salt.encode('utf-8'), 100000)
        return f"pbkdf2_sha256$100000${salt}${key.hex()}"
    
    def _verify_password(self, password, encrypted):
        """Verify password against encrypted version"""
        if not password or not encrypted:
            return False
        
        try:
            algorithm, iterations, salt, key = encrypted.split('$')
            if algorithm != 'pbkdf2_sha256':
                return False
            
            new_key = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'),
                                        salt.encode('utf-8'), int(iterations))
            return new_key.hex() == key
        except ValueError:
            return False
    
    def _generate_api_key(self):
        """Generate a secure API key"""
        return secrets.token_urlsafe(32)
    
    async def authenticate(self, login, password):
        """Authenticate user with login and password"""
        if not login or not password:
            return False
        
        # Find user by login
        user = await self.search([('login', '=', login), ('active', '=', True)], limit=1)
        if not user:
            return False
        
        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.now():
            return False
        
        # Verify password
        if not self._verify_password(password, user.password_crypt):
            # Increment failed attempts
            await user.write({
                'login_attempts': user.login_attempts + 1
            })
            
            # Lock account after 5 failed attempts
            if user.login_attempts >= 5:
                await user.write({
                    'locked_until': datetime.now() + timedelta(minutes=30)
                })
            
            return False
        
        # Reset failed attempts on successful login
        await user.write({
            'login_attempts': 0,
            'locked_until': False,
            'login_date': datetime.now()
        })
        
        return user
    
    async def create_session(self):
        """Create a new session for the user"""
        session_token = secrets.token_urlsafe(32)
        session_expiry = datetime.now() + timedelta(hours=24)  # 24 hour sessions
        
        await self.write({
            'session_token': session_token,
            'session_expiry': session_expiry
        })
        
        return session_token
    
    async def validate_session(self, session_token):
        """Validate a session token"""
        if not session_token:
            return False
        
        if (self.session_token == session_token and 
            self.session_expiry and 
            self.session_expiry > datetime.now()):
            return True
        
        return False
    
    async def invalidate_session(self):
        """Invalidate current session"""
        await self.write({
            'session_token': False,
            'session_expiry': False
        })
    
    async def has_group(self, group_xml_id):
        """Check if user has a specific group"""
        for group in self.groups_id:
            if await group.has_group(group_xml_id):
                return True
        return False
    
    async def get_user_companies(self):
        """Get companies this user has access to"""
        # In a multi-company setup, this would return accessible companies
        # For now, return the user's company
        if self.partner_id.parent_id and self.partner_id.parent_id.is_company:
            return [self.partner_id.parent_id]
        elif self.partner_id.is_company:
            return [self.partner_id]
        else:
            # Return default company or create one
            return []
    
    async def name_get(self):
        """Return name representation for this user"""
        result = []
        for user in self:
            name = user.name or user.login or ''
            result.append((user.id, name))
        return result
